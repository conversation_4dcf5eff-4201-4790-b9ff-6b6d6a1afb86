#!/bin/bash

BASE_URL="http://localhost:5001/api/v1"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTE5M2M1MDI0OWUyNWQ4NzYyY2Y1NyIsImlhdCI6MTc1NDM3MTAxNCwiZXhwIjoxNzU0Mzc0NjE0fQ._QoVF9Laz0vXnRpjUlQ5jEnhkoVCGAvXEKZA20mKKTY"
FARM_ID="689193c60249e25d8762cf59"

echo "💰 Creating financial records..."

# Create income records manually
dates=("2025-08-02" "2025-07-30" "2025-07-27" "2025-07-24" "2025-07-21" "2025-07-18" "2025-07-15" "2025-07-12" "2025-07-09" "2025-07-06")
amounts=(1080 1200 960 1350 1140 1260 1020 1170 1080 1320)

for i in {0..9}; do
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/financial-records" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${dates[$i]}\",
            \"type\": \"income\",
            \"category\": \"Egg Sales\",
            \"amount\": ${amounts[$i]},
            \"description\": \"Egg sales - 3 day batch\",
            \"notes\": \"Sold to local market\"
        }" > /dev/null
    
    echo "✅ Created income record for ${dates[$i]} (${amounts[$i]} NPR)"
done

# Create expense records
exp_dates=("2025-08-01" "2025-07-28" "2025-07-25" "2025-07-20" "2025-07-15" "2025-07-10" "2025-07-05" "2025-06-30")
exp_categories=("Feed" "Medicine" "Equipment" "Labor" "Feed" "Utilities" "Medicine" "Feed")
exp_amounts=(3200 450 1500 2000 3100 600 350 2900)

for i in {0..7}; do
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/financial-records" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${exp_dates[$i]}\",
            \"type\": \"expense\",
            \"category\": \"${exp_categories[$i]}\",
            \"amount\": ${exp_amounts[$i]},
            \"description\": \"${exp_categories[$i]} expense\",
            \"notes\": \"Monthly cost\"
        }" > /dev/null
    
    echo "✅ Created expense record for ${exp_dates[$i]} (${exp_categories[$i]}: ${exp_amounts[$i]} NPR)"
done

echo "✅ Financial data creation completed!"
