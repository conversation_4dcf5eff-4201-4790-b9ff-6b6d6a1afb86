#!/bin/bash

BASE_URL="http://localhost:5001/api/v1"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTE5M2M1MDI0OWUyNWQ4NzYyY2Y1NyIsImlhdCI6MTc1NDM3MTAxNCwiZXhwIjoxNzU0Mzc0NjE0fQ._QoVF9Laz0vXnRpjUlQ5jEnhkoVCGAvXEKZA20mKKTY"
FARM_ID="689193c60249e25d8762cf59"

echo "🚀 Starting AI insights test data population..."

# Function to get date N days ago
get_date_days_ago() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        date -v-${1}d +%Y-%m-%d
    else
        # Linux
        date -d "${1} days ago" +%Y-%m-%d
    fi
}

# Function to generate random number between min and max
random_between() {
    echo $(( RANDOM % ($2 - $1 + 1) + $1 ))
}

echo "📦 Creating chickens..."
# Create 20 chickens with different breeds
breeds=("Rhode Island Red" "Leghorn" "Sussex" "Orpington" "Australorp")
for i in {1..20}; do
    breed_index=$(random_between 0 4)
    breed=${breeds[$breed_index]}
    days_old=$(random_between 30 365)
    hatch_date=$(get_date_days_ago $days_old)
    
    status="healthy"
    if [ $(random_between 1 10) -gt 8 ]; then
        status="sick"
    fi
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/chickens" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"breed\": \"${breed}\",
            \"hatchDate\": \"${hatch_date}\",
            \"status\": \"${status}\",
            \"notes\": \"Chicken ${i} - Good layer\"
        }" > /dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ Created chicken ${i}/20 (${breed})"
    else
        echo "❌ Failed to create chicken ${i}"
    fi
done

echo "🥚 Creating egg production records..."
# Create 60 days of egg production data
for days in {60..0}; do
    date=$(get_date_days_ago $days)
    base_production=35
    variation=$(random_between -8 8)
    quantity=$((base_production + variation))
    if [ $quantity -lt 0 ]; then
        quantity=0
    fi
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/egg-production" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"quantity\": ${quantity},
            \"notes\": \"Daily collection - ${days} days ago\"
        }" > /dev/null
    
    if [ $((days % 10)) -eq 0 ]; then
        echo "✅ Created egg production for ${date} (${quantity} eggs)"
    fi
done

echo "🌾 Creating feed records..."
# Create weekly feed records for 12 weeks
feed_types=("Layer Feed" "Starter Feed" "Grower Feed" "Organic Feed")
for weeks in {12..0}; do
    days=$((weeks * 7))
    date=$(get_date_days_ago $days)
    feed_index=$(random_between 0 3)
    feed_type=${feed_types[$feed_index]}
    quantity=$(random_between 40 60)
    cost_per_kg=$(random_between 45 65)
    total_cost=$((quantity * cost_per_kg))
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/feed-records" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"feedType\": \"${feed_type}\",
            \"quantity\": ${quantity},
            \"cost\": ${total_cost},
            \"supplier\": \"Local Feed Store\",
            \"notes\": \"Weekly feed purchase - ${feed_type}\"
        }" > /dev/null
    
    echo "✅ Created feed record for ${date} (${quantity}kg ${feed_type})"
done

echo "💰 Creating financial records..."
# Create income records (egg sales) - every 3 days for 60 days
for days in {60..0..3}; do
    date=$(get_date_days_ago $days)
    eggs_per_day=$(random_between 25 45)
    price_per_egg=$(random_between 8 12)
    amount=$((eggs_per_day * price_per_egg * 3))
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/financial-records" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"type\": \"income\",
            \"category\": \"Egg Sales\",
            \"amount\": ${amount},
            \"description\": \"Egg sales - $((eggs_per_day * 3)) eggs over 3 days\",
            \"notes\": \"Sold to local market at ${price_per_egg} NPR per egg\"
        }" > /dev/null
    
    if [ $((days % 15)) -eq 0 ]; then
        echo "✅ Created income record for ${date} (${amount} NPR)"
    fi
done

# Create expense records
categories=("Feed" "Medicine" "Equipment" "Labor" "Utilities")
for i in {1..15}; do
    days=$(random_between 1 60)
    date=$(get_date_days_ago $days)
    cat_index=$(random_between 0 4)
    category=${categories[$cat_index]}
    
    case $category in
        "Feed")
            amount=$(random_between 2000 4000)
            ;;
        "Medicine")
            amount=$(random_between 200 1000)
            ;;
        "Equipment")
            amount=$(random_between 500 3000)
            ;;
        "Labor")
            amount=$(random_between 1000 2500)
            ;;
        "Utilities")
            amount=$(random_between 300 800)
            ;;
    esac
    
    curl -s -X POST "${BASE_URL}/farms/${FARM_ID}/financial-records" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "Content-Type: application/json" \
        -d "{
            \"date\": \"${date}\",
            \"type\": \"expense\",
            \"category\": \"${category}\",
            \"amount\": ${amount},
            \"description\": \"${category} expense\",
            \"notes\": \"Monthly ${category,,} cost\"
        }" > /dev/null
    
    echo "✅ Created expense record ${i}/15 (${category}: ${amount} NPR)"
done

echo "🎉 AI insights test data population completed successfully!"
echo "📊 Data summary:"
echo "   - 20 chickens created"
echo "   - 61 days of egg production data"
echo "   - 13 weeks of feed records"
echo "   - 20+ income records"
echo "   - 15 expense records"
echo ""
echo "🤖 Ready for AI insights testing with Gemini API!"
