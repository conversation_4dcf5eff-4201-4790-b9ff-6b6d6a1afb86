{"register": "Register", "name": "Name", "email": "Email", "password": "Password", "registering": "Registering...", "login": "<PERSON><PERSON>", "registration_failed": "Registration failed. Please try again.", "dashboard": "Dashboard", "chickens": "Chickens", "egg_production": "Egg Production", "feed_records": "Feed Records", "health_records": "Health Records", "financial_records": "Financial Records", "suppliers": "Suppliers", "customers": "Customers", "ai_insight_history": "AI Insight History", "logout": "Logout", "select_farm": "Select Farm", "create_farm": "Create Farm", "farm_name": "Farm Name", "location": "Location", "selected_farm": "Selected Farm", "trigger_test_low_feed_alert": "Trigger Test Low Fe<PERSON>", "test_low_feed_alert_message": "Test low feed alert triggered successfully!", "failed_to_trigger_alert": "Failed to trigger alert", "failed_to_fetch_dashboard_data": "Failed to fetch dashboard data", "add_new_chicken": "Add New Chicken", "add_new_egg_record": "Add New Egg Record", "add_new_feed_record": "Add New Feed Record", "add_new_health_record": "Add New Health Record", "add_new_financial_record": "Add New Financial Record", "no_chickens_found": "No chickens found", "no_egg_records_found": "No egg records found", "no_feed_records_found": "No feed records found", "no_health_records_found": "No health records found", "no_financial_records_found": "No financial records found", "failed_to_save_chicken": "Failed to save chicken", "failed_to_save_egg_record": "Failed to save egg record", "breed": "Breed", "hatch_date": "Hatch Date", "status": "Status", "healthy": "Healthy", "sick": "Sick", "laying": "Laying", "sold": "Sold", "deceased": "Deceased", "date": "Date", "quantity": "Quantity", "chicken_group": "Chicken Group", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "actions": "Actions", "loading": "Loading", "auth": {"login_title": "Sign in to your account", "register_title": "Create a new account", "forgot_password": "Forgot your password?", "no_account": "Don't have an account?", "have_account": "Already have an account?", "sign_in": "Sign in", "sign_up": "Sign up", "remember_me": "Remember me"}, "validation": {"required": "This field is required", "email_invalid": "Please enter a valid email address", "password_length": "Password must be at least 6 characters long", "breed_required": "Breed is required", "hatch_date_required": "Hatch date is required", "date_required": "Date is required", "quantity_required_positive": "Quantity must be a positive number"}}