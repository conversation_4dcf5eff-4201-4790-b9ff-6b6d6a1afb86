const axios = require('axios');

const BASE_URL = 'http://localhost:5001/api/v1';
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4OTE5M2M1MDI0OWUyNWQ4NzYyY2Y1NyIsImlhdCI6MTc1NDM3MTAxNCwiZXhwIjoxNzU0Mzc0NjE0fQ._QoVF9Laz0vXnRpjUlQ5jEnhkoVCGAvXEKZA20mKKTY';
const FARM_ID = '689193c60249e25d8762cf59';

const config = {
  headers: {
    'Authorization': `Bearer ${TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// Helper function to generate dates in the past
function getDateDaysAgo(days) {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date.toISOString().split('T')[0];
}

// Helper function to generate random number within range
function randomBetween(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function populateTestData() {
  console.log('🚀 Starting AI insights test data population...');
  
  try {
    // 1. Create chickens with different breeds and ages
    console.log('📦 Creating chickens...');
    const chickenBreeds = ['Rhode Island Red', 'Leghorn', 'Sussex', 'Orpington', 'Australorp'];
    const chickenIds = [];
    
    for (let i = 0; i < 50; i++) {
      const hatchDate = new Date();
      hatchDate.setDate(hatchDate.getDate() - randomBetween(30, 365)); // 1 month to 1 year old
      
      const chickenData = {
        breed: chickenBreeds[randomBetween(0, chickenBreeds.length - 1)],
        hatchDate: hatchDate.toISOString().split('T')[0],
        status: randomBetween(1, 10) > 8 ? 'sick' : 'healthy', // 20% chance of being sick
        notes: `Chicken ${i + 1} - Good layer`
      };
      
      try {
        const response = await axios.post(`${BASE_URL}/farms/${FARM_ID}/chickens`, chickenData, config);
        chickenIds.push(response.data.chicken._id);
        console.log(`✅ Created chicken ${i + 1}/50`);
      } catch (error) {
        console.log(`❌ Failed to create chicken ${i + 1}:`, error.response?.data?.message || error.message);
      }
    }
    
    // 2. Create historical egg production data (6 months)
    console.log('🥚 Creating egg production records...');
    for (let days = 180; days >= 0; days--) {
      const date = getDateDaysAgo(days);
      const baseProduction = 35; // Base eggs per day
      const seasonalVariation = Math.sin((days / 365) * 2 * Math.PI) * 5; // Seasonal variation
      const randomVariation = randomBetween(-8, 8); // Daily random variation
      const quantity = Math.max(0, Math.floor(baseProduction + seasonalVariation + randomVariation));
      
      const eggData = {
        date: date,
        quantity: quantity,
        notes: days === 0 ? 'Today\'s collection' : `Historical data - ${days} days ago`
      };
      
      try {
        await axios.post(`${BASE_URL}/farms/${FARM_ID}/egg-production`, eggData, config);
        if (days % 30 === 0) console.log(`✅ Created egg production for ${date} (${quantity} eggs)`);
      } catch (error) {
        console.log(`❌ Failed to create egg production for ${date}:`, error.response?.data?.message || error.message);
      }
    }
    
    // 3. Create feed records
    console.log('🌾 Creating feed records...');
    const feedTypes = ['Layer Feed', 'Starter Feed', 'Grower Feed', 'Organic Feed'];
    for (let days = 180; days >= 0; days -= 7) { // Weekly feed records
      const date = getDateDaysAgo(days);
      const feedType = feedTypes[randomBetween(0, feedTypes.length - 1)];
      const quantity = randomBetween(40, 60); // kg per week
      const costPerKg = randomBetween(45, 65); // NPR per kg
      
      const feedData = {
        date: date,
        feedType: feedType,
        quantity: quantity,
        cost: quantity * costPerKg,
        supplier: 'Local Feed Store',
        notes: `Weekly feed purchase - ${feedType}`
      };
      
      try {
        await axios.post(`${BASE_URL}/farms/${FARM_ID}/feed-records`, feedData, config);
        console.log(`✅ Created feed record for ${date} (${quantity}kg ${feedType})`);
      } catch (error) {
        console.log(`❌ Failed to create feed record for ${date}:`, error.response?.data?.message || error.message);
      }
    }
    
    // 4. Create health records
    console.log('🏥 Creating health records...');
    const healthTypes = ['vaccination', 'treatment', 'checkup'];
    const treatments = ['Deworming', 'Vitamin Supplement', 'Antibiotic Treatment', 'Newcastle Vaccine', 'Fowl Pox Vaccine'];
    
    for (let i = 0; i < 25; i++) {
      const days = randomBetween(1, 180);
      const date = getDateDaysAgo(days);
      const chickenId = chickenIds[randomBetween(0, chickenIds.length - 1)];
      const type = healthTypes[randomBetween(0, healthTypes.length - 1)];
      const treatment = treatments[randomBetween(0, treatments.length - 1)];
      const cost = randomBetween(50, 500); // NPR
      
      const healthData = {
        date: date,
        chickenId: chickenId,
        type: type,
        description: treatment,
        treatment: treatment,
        cost: cost,
        notes: `${type} - ${treatment}`
      };
      
      try {
        await axios.post(`${BASE_URL}/farms/${FARM_ID}/health-records`, healthData, config);
        console.log(`✅ Created health record ${i + 1}/25 (${treatment})`);
      } catch (error) {
        console.log(`❌ Failed to create health record ${i + 1}:`, error.response?.data?.message || error.message);
      }
    }
    
    // 5. Create financial records
    console.log('💰 Creating financial records...');
    
    // Income records (egg sales)
    for (let days = 180; days >= 0; days -= 3) { // Every 3 days
      const date = getDateDaysAgo(days);
      const eggsPerDay = randomBetween(25, 45);
      const pricePerEgg = randomBetween(8, 12); // NPR per egg
      const amount = eggsPerDay * pricePerEgg * 3; // 3 days worth
      
      const incomeData = {
        date: date,
        type: 'income',
        category: 'Egg Sales',
        amount: amount,
        description: `Egg sales - ${eggsPerDay * 3} eggs over 3 days`,
        notes: `Sold to local market at ${pricePerEgg} NPR per egg`
      };
      
      try {
        await axios.post(`${BASE_URL}/farms/${FARM_ID}/financial-records`, incomeData, config);
        if (days % 30 === 0) console.log(`✅ Created income record for ${date} (${amount} NPR)`);
      } catch (error) {
        console.log(`❌ Failed to create income record for ${date}:`, error.response?.data?.message || error.message);
      }
    }
    
    // Expense records
    const expenseCategories = ['Feed', 'Medicine', 'Equipment', 'Labor', 'Utilities'];
    for (let i = 0; i < 40; i++) {
      const days = randomBetween(1, 180);
      const date = getDateDaysAgo(days);
      const category = expenseCategories[randomBetween(0, expenseCategories.length - 1)];
      let amount;
      
      switch (category) {
        case 'Feed':
          amount = randomBetween(2000, 4000);
          break;
        case 'Medicine':
          amount = randomBetween(200, 1000);
          break;
        case 'Equipment':
          amount = randomBetween(500, 3000);
          break;
        case 'Labor':
          amount = randomBetween(1000, 2500);
          break;
        case 'Utilities':
          amount = randomBetween(300, 800);
          break;
        default:
          amount = randomBetween(200, 1000);
      }
      
      const expenseData = {
        date: date,
        type: 'expense',
        category: category,
        amount: amount,
        description: `${category} expense`,
        notes: `Monthly ${category.toLowerCase()} cost`
      };
      
      try {
        await axios.post(`${BASE_URL}/farms/${FARM_ID}/financial-records`, expenseData, config);
        console.log(`✅ Created expense record ${i + 1}/40 (${category}: ${amount} NPR)`);
      } catch (error) {
        console.log(`❌ Failed to create expense record ${i + 1}:`, error.response?.data?.message || error.message);
      }
    }
    
    console.log('🎉 AI insights test data population completed successfully!');
    console.log('📊 Data summary:');
    console.log(`   - ${chickenIds.length} chickens created`);
    console.log('   - 181 days of egg production data');
    console.log('   - 26 weeks of feed records');
    console.log('   - 25 health records');
    console.log('   - 60+ income records');
    console.log('   - 40 expense records');
    console.log('');
    console.log('🤖 Ready for AI insights testing with Gemini API!');
    
  } catch (error) {
    console.error('❌ Error during data population:', error.message);
  }
}

// Run the population script
populateTestData();
