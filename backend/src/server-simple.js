require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const http = require('http');
const { Server } = require('socket.io');

const app = express();
const httpServer = http.createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.CLIENT_URL || 'http://localhost:5173',
    methods: ['GET', 'POST'],
  },
});

// Make io accessible to routes
app.set('socketio', io);

// Basic middleware
app.use(express.json());
app.use(cors());

// Database Connection
const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/smartpoultry';
mongoose.connect(mongoURI)
  .then(() => {
    console.log('MongoDB connected successfully!');
  })
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('A user connected');

  socket.on('joinFarmRoom', (farmId) => {
    socket.join(farmId);
    console.log(`User joined farm room: ${farmId}`);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected');
  });
});

// Import routes
const authRoutes = require('./routes/authRoutes');
const farmRoutes = require('./routes/farmRoutes');
const chickenRoutes = require('./routes/chickenRoutes');
const eggProductionRoutes = require('./routes/eggProductionRoutes');
const feedRecordRoutes = require('./routes/feedRecordRoutes');
const healthRecordRoutes = require('./routes/healthRecordRoutes');
const financialRecordRoutes = require('./routes/financialRecordRoutes');
const supplierRoutes = require('./routes/supplierRoutes');
const customerRoutes = require('./routes/customerRoutes');
const aiInsightLogRoutes = require('./routes/aiInsightLogRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const dashboardRoutes = require('./routes/dashboardRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const userRoutes = require('./routes/userRoutes');

// Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/farms', farmRoutes);
app.use('/api/v1/farms/:farmId/chickens', chickenRoutes);
app.use('/api/v1/farms/:farmId/egg-production', eggProductionRoutes);
app.use('/api/v1/farms/:farmId/feed-records', feedRecordRoutes);
app.use('/api/v1/farms/:farmId/health-records', healthRecordRoutes);
app.use('/api/v1/farms/:farmId/financial-records', financialRecordRoutes);
app.use('/api/v1/farms/:farmId/suppliers', supplierRoutes);
app.use('/api/v1/farms/:farmId/customers', customerRoutes);
app.use('/api/v1/farms/:farmId/ai-insights', aiInsightLogRoutes);
app.use('/api/v1/farms/:farmId/analytics', analyticsRoutes);
app.use('/api/v1/farms/:farmId/dashboard', dashboardRoutes);
app.use('/api/v1/farms/:farmId/notifications', notificationRoutes);
app.use('/api/v1/users', userRoutes);

app.get('/', (req, res) => {
  res.send('SmartPoultry Backend API is running!');
});

// Health check endpoint
app.get('/health', (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
    database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    environment: process.env.NODE_ENV || 'development'
  };

  try {
    res.status(200).json(healthCheck);
  } catch (error) {
    healthCheck.message = error;
    res.status(503).json(healthCheck);
  }
});

// Error Handling Middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something broke!' });
});

const PORT = process.env.PORT || 5000;
httpServer.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
