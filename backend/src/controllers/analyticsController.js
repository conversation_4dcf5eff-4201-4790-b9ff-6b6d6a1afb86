const { GoogleGenerativeAI } = require('@google/generative-ai');
const EggProduction = require('../models/EggProduction');
const FinancialRecord = require('../models/FinancialRecord');
const Farm = require('../models/Farm');
const AIInsightLog = require('../models/AIInsightLog'); // Import the new model

// Access your API key as an environment variable (see .env.example for instructions)
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Helper function to check if user owns the farm
const checkFarmOwnership = async (farmId, userId) => {
  const farm = await Farm.findById(farmId);
  if (!farm || farm.owner.toString() !== userId.toString()) {
    return false;
  }
  return true;
};

// @desc    Get AI-generated daily summary report for a farm
// @route   GET /api/v1/farms/:farmId/analytics/daily-summary
// @access  Private
exports.getDailySummary = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!(await checkFarmOwnership(farmId, req.user.id))) {
      return res.status(403).json({ message: 'Not authorized to access this farm' });
    }

    // Fetch relevant data for the summary
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const eggProductionToday = await EggProduction.find({
      farm: farmId,
      date: { $gte: today, $lt: tomorrow },
    });

    const financialRecordsToday = await FinancialRecord.find({
      farm: farmId,
      date: { $gte: today, $lt: tomorrow },
    });

    const totalEggs = eggProductionToday.reduce((sum, record) => sum + record.quantity, 0);
    const totalIncome = financialRecordsToday.filter(rec => rec.type === 'income').reduce((sum, rec) => sum + rec.amount, 0);
    const totalExpense = financialRecordsToday.filter(rec => rec.type === 'expense').reduce((sum, rec) => sum + rec.amount, 0);

    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    const prompt = `Generate a concise daily summary for a poultry farm based on the following data:
    - Total eggs collected today: ${totalEggs}
    - Total income today: ${totalIncome} NPR
    - Total expenses today: ${totalExpense} NPR

    Highlight any significant observations or suggestions. Keep it under 100 words.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Save the insight to log
    await AIInsightLog.create({
      farm: farmId,
      type: 'daily_summary',
      content: text,
      rawData: { totalEggs, totalIncome, totalExpense },
    });

    res.status(200).json({ summary: text });
  } catch (error) {
    console.error('Error generating daily summary:', error);
    res.status(500).json({ message: 'Error generating daily summary' });
  }
};

// @desc    Forecast egg production trends for a farm
// @route   GET /api/v1/farms/:farmId/analytics/egg-forecast
// @access  Private
exports.getEggProductionForecast = async (req, res) => {
  try {
    const { farmId } = req.params;

    if (!(await checkFarmOwnership(farmId, req.user.id))) {
      return res.status(403).json({ message: 'Not authorized to access this farm' });
    }

    // Fetch historical egg production data (e.g., last 90 days)
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    const historicalData = await EggProduction.find({
      farm: farmId,
      date: { $gte: ninetyDaysAgo },
    }).sort({ date: 1 });

    if (historicalData.length < 10) { // Need a reasonable amount of data for forecasting
      return res.status(400).json({ message: 'Not enough historical data for accurate forecasting (min 10 records required)' });
    }

    // Format data for Gemini API
    const formattedData = historicalData.map(record => `Date: ${record.date.toISOString().split('T')[0]}, Quantity: ${record.quantity}`).join('\n');

    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

    const prompt = `Analyze the following historical daily egg production data for a poultry farm and forecast the trend for the next 7 days. Provide the forecast in a clear, concise paragraph, mentioning any patterns or anomalies observed. Data:
${formattedData}

Forecast:`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    // Save the insight to log
    await AIInsightLog.create({
      farm: farmId,
      type: 'egg_forecast',
      content: text,
      rawData: historicalData.map(record => ({ date: record.date, quantity: record.quantity })),
    });

    res.status(200).json({ forecast: text });
  } catch (error) {
    console.error('Error generating egg production forecast:', error);
    res.status(500).json({ message: 'Error generating egg production forecast' });
  }
};