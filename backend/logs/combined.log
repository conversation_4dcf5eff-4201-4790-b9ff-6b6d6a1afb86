{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-03T17:31:44.981Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-03T17:33:08.854Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-03T17:33:43.229Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-03T17:33:56.766Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-03T17:34:23.548Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-03T17:34:50.006Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-03T17:34:59.407Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T14:35:37.367Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T14:37:05.590Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T14:38:31.373Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:46:43.543Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:47:21.435Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:47:27.656Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:47:36.009Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:48:18.588Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:48:18.588Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:48:24.673Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:48:42.939Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:49:03.777Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:49:20.542Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:52:58.705Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:52:58.707Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:52:58.706Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:52:58.705Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:52:58.705Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:53:07.876Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:54:19.415Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:54:19.420Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:54:19.418Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:54:19.418Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:54:19.418Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:54:19.455Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:55:21.707Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:55:37.275Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:55:50.634Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:56:14.865Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:56:20.245Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:17.632Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:17.632Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:17.632Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:17.635Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:17.633Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:17.635Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:24.004Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T16:59:53.475Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:00:56.102Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:01:13.125Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.642Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.757Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.758Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.764Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.764Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.785Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.817Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.817Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.919Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:13.919Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.073Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.073Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.276Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.276Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.528Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.529Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.831Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:14.831Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:15.187Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:15.187Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:15.590Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:15.591Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:16.043Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:16.043Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:16.545Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:16.546Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:17.097Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:17.098Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:17.699Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:17.700Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:18.352Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:18.352Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:19.054Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:19.054Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:19.807Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:19.807Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:20.610Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:20.611Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:21.463Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:21.463Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:22.366Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:22.366Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:23.317Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:23.318Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:24.319Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:24.320Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:25.372Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:25.373Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:26.476Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:26.477Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:27.628Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:27.629Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:28.830Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:28.830Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:30.081Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:30.082Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:31.383Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:31.383Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:32.736Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:32.736Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:34.138Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:34.139Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:35.590Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:35.590Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:37.091Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:37.092Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:38.645Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:38.645Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:40.247Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:40.248Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:41.900Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:41.901Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:43.604Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:43.604Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:45.356Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:45.356Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at sanitizeInput (/Users/<USER>/SmartPoultry/backend/src/middleware/security.js:82:18)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at internalNext (/Users/<USER>/SmartPoultry/backend/node_modules/helmet/index.cjs:531:6)","timestamp":"2025-08-04T17:06:45.438Z"}
{"duration":"6ms","environment":"development","ip":"::1","level":"warn","message":"HTTP Request","method":"GET","service":"smartpoultry-backend","status":500,"timestamp":"2025-08-04T17:06:45.443Z","url":"/health","userAgent":"curl/8.7.1"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:47.160Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:47.160Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:49.013Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:49.014Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:50.916Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:50.917Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:52.870Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:52.870Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:54.873Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:54.873Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:56.876Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:56.876Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:58.880Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:06:58.881Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:00.882Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:00.882Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:02.884Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:02.885Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:04.888Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:04.888Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:06.891Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:06.891Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:08.892Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:08.892Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:10.895Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:10.895Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:12.897Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:12.898Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:14.900Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:14.900Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:16.901Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:16.902Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:18.905Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:18.906Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:20.909Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:20.910Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:22.913Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:22.913Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:24.917Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:24.918Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:26.920Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:26.921Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:28.923Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:28.924Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:30.926Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:30.926Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:32.929Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:32.930Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:34.934Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:34.934Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:36.936Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:36.937Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:39.424Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.067Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.075Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.092Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.093Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.196Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.197Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.203Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.317Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.317Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.470Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.470Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.674Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.674Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.924Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:40.925Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:41.227Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:41.227Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:41.581Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:41.581Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:41.985Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:41.985Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:42.438Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:42.439Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:42.940Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:42.940Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:43.493Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:43.494Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:44.097Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:44.097Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:44.750Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:44.750Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:45.454Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:45.454Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:46.207Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:46.207Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:47.020Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:47.021Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:47.874Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:47.874Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:48.777Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:48.777Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:49.729Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:49.729Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:50.731Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:50.731Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:51.784Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:51.784Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:52.886Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:52.887Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:54.040Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:54.040Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:55.242Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:55.242Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:56.494Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:56.494Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:57.797Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:57.797Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:59.150Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:07:59.150Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:00.553Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:00.554Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:02.007Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:02.007Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:03.510Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:03.510Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:05.063Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:05.063Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:06.665Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:06.665Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:12.998Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.109Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.109Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.115Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.115Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.118Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.168Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.169Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.271Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.271Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.424Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.425Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.626Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.627Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.879Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:13.879Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:14.182Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:14.182Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:14.534Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:14.534Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:14.936Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:14.936Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:15.389Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:15.389Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:15.889Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:15.889Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:16.443Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:16.443Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:17.048Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:17.048Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:17.702Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:17.702Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:18.403Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:18.404Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:19.156Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:19.156Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:19.959Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:19.959Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:20.813Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:20.813Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:21.715Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:21.715Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:22.669Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:22.670Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:23.684Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:23.692Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:24.746Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:24.746Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:25.848Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:25.848Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:27.001Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:27.001Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:28.203Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:28.203Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at sanitizeInput (/Users/<USER>/SmartPoultry/backend/src/middleware/security.js:87:3)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at internalNext (/Users/<USER>/SmartPoultry/backend/node_modules/helmet/index.cjs:531:6)","timestamp":"2025-08-04T17:08:28.908Z"}
{"duration":"3ms","environment":"development","ip":"::1","level":"warn","message":"HTTP Request","method":"GET","service":"smartpoultry-backend","status":500,"timestamp":"2025-08-04T17:08:28.910Z","url":"/health","userAgent":"curl/8.7.1"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:29.456Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:29.456Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:30.758Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:30.759Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:32.111Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:32.112Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:33.513Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:33.514Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:34.965Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:34.966Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:36.469Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:36.469Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:38.023Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:38.023Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:39.625Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:39.625Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:41.277Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:41.278Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:42.981Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:42.981Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:44.734Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:44.734Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:46.537Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:46.538Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:48.390Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:48.390Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:50.291Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:50.292Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:52.244Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:52.244Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:54.247Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:54.248Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:56.249Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:56.250Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:58.251Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:08:58.252Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:00.253Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:00.254Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.166Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.444Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.444Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.450Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.451Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.455Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.504Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.504Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.607Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.607Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.759Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.759Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.961Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:03.961Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:04.213Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:04.213Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:04.514Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:04.515Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:04.866Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:04.866Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:05.267Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:05.267Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:05.720Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:05.720Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:06.222Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:06.222Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:06.773Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:06.774Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:07.376Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:07.376Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:08.036Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:08.036Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:08.737Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:08.738Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:09.491Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:09.492Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:10.293Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:10.293Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:11.145Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:11.146Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:12.054Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:12.054Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:13.006Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:13.007Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:14.009Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:14.010Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:15.061Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:15.062Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:16.164Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:16.165Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:17.317Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:17.318Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:18.521Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:18.521Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:19.773Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:19.774Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:21.076Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:21.076Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:22.428Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:22.428Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:23.831Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:23.831Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:25.283Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:25.283Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:26.785Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:26.785Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.521Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.632Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.633Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.640Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.640Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.644Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.692Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.692Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.794Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.794Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.946Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:33.947Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:34.147Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:34.148Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:34.399Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:34.400Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:34.702Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:34.703Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:35.056Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:35.056Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:35.459Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:35.460Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:35.912Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:35.912Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:36.414Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:36.415Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:36.966Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:36.967Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:37.568Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:37.568Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:38.220Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:38.220Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:38.922Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:38.922Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:39.674Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:39.675Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:40.478Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:40.478Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:41.331Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:41.331Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:42.234Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:42.234Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:43.185Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:43.186Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:44.188Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:44.188Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:45.240Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:45.240Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:46.342Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:46.342Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:47.494Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:47.495Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:48.700Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:48.701Z"}
{"duration":"5ms","environment":"development","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"smartpoultry-backend","status":200,"timestamp":"2025-08-04T17:09:48.760Z","url":"/health","userAgent":"curl/8.7.1"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:49.952Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:49.953Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:51.254Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:51.255Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:52.607Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:52.607Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:54.008Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:54.008Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:55.459Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:55.460Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:56.962Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:56.963Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:58.514Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:09:58.515Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:00.117Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:00.117Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:01.768Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:01.768Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:03.471Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:03.471Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:05.223Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:05.223Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:07.025Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:07.025Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:08.877Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:08.878Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:10.780Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:10.781Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:12.732Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:12.733Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:14.735Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:14.735Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:16.738Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:16.738Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:18.741Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:18.741Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:20.743Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:20.743Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:22.746Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:22.746Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:24.747Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:24.748Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:26.750Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:26.751Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:28.752Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:28.753Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:30.756Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:30.757Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:32.759Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:32.759Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:34.761Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:34.761Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:36.764Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:36.765Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:38.766Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:38.766Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:40.768Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:40.768Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:42.772Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:42.772Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:44.775Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:44.776Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:46.779Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:46.779Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:48.782Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:48.783Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:50.784Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:50.785Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:52.797Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:52.797Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:54.807Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:54.807Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:56.809Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:56.809Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:58.816Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:10:58.817Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:00.819Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:00.819Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:02.821Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:02.821Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:04.823Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:04.823Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:06.824Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:06.825Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.403Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.543Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.544Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.548Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.549Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.553Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.600Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.601Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.703Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.703Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.855Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:09.856Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.057Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.057Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.310Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.310Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.612Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.612Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.963Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:10.963Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:11.365Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:11.365Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:11.817Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:11.817Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:12.319Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:12.320Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:12.871Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:12.871Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:13.473Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:13.473Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:14.125Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:14.125Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:14.827Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:14.827Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:15.579Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:15.580Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:16.382Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:16.383Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:17.235Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:17.235Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:18.136Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:18.136Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:19.088Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:19.088Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:20.090Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:20.090Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:21.146Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:21.146Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:22.248Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:22.249Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:23.403Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:23.404Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:24.614Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:24.615Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:25.868Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:25.868Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:27.169Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:27.169Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:28.522Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:28.522Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:29.923Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:29.923Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:31.377Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:31.377Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:32.880Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:32.881Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:34.431Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:34.431Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:36.032Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:36.033Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:37.685Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:37.685Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:39.395Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:39.395Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:41.147Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:41.149Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:42.956Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:42.956Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:44.809Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:44.810Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:46.714Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:46.715Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:48.669Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:48.670Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:50.672Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:50.672Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:52.674Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:52.675Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:54.678Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:54.678Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:56.683Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:56.684Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:58.684Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:11:58.685Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:00.686Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:00.686Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:02.690Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:02.692Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:04.693Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:04.693Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:06.696Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:06.696Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:08.698Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:08.699Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:10.703Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:10.704Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:12.706Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:12.706Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:14.707Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:14.708Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:16.714Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:16.714Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:18.720Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:18.721Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:20.722Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:20.723Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:22.724Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:22.724Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:24.727Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:24.727Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:26.729Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:26.730Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:28.734Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:28.737Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:30.739Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:30.739Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:32.746Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:32.747Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:34.749Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:34.749Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:36.834Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:36.842Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:38.846Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:38.847Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:40.873Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:40.874Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.169Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.419Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.420Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.426Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.427Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.434Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.478Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.478Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.581Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.581Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.733Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.734Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.938Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:42.939Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:43.190Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:43.190Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:43.492Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:43.493Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:43.844Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:43.845Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:44.246Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:44.246Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:44.698Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:44.698Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:45.201Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:45.201Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:45.753Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:45.754Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:46.360Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:46.360Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:47.014Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:47.015Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:47.717Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:47.718Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:48.469Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:48.469Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:49.271Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:49.272Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:50.124Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:50.124Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:51.027Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:51.027Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:51.978Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:51.979Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:52.981Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:52.981Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:54.034Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:54.034Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:55.136Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:55.136Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:56.288Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:56.289Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:57.491Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:57.491Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:58.743Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:12:58.743Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:00.050Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:00.050Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:01.403Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:01.403Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:02.805Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:02.805Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:04.257Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:04.257Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:05.758Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:05.759Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:07.312Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:07.313Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:08.915Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:08.916Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:10.583Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:10.584Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:12.285Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:12.285Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:14.038Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:14.038Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:15.840Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:15.840Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:17.691Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:17.691Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:19.594Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:19.595Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:21.546Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:21.546Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:23.549Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:23.549Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:25.551Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:25.551Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:27.552Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:27.552Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:29.554Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:29.554Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:31.557Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:31.558Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:33.559Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:33.560Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:35.562Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:35.562Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:37.568Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:37.569Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:39.573Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:39.573Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:41.577Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:41.577Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:43.580Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:43.580Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:45.588Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:45.590Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:47.594Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:47.594Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:49.616Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:49.616Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:51.620Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:51.624Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:53.745Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.568Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.711Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.712Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.721Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.724Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.735Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.776Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.776Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.877Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:54.877Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:55.029Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:55.029Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:55.230Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:55.231Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:55.949Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:55.970Z"}
{"environment":"development","level":"info","message":"Server running on port 5001","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.095Z"}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.096Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.112Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.112Z"}
{"environment":"development","level":"info","message":"MongoDB Connected: localhost","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.137Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.167Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.167Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.272Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.272Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.425Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.425Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.627Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.628Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.881Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:56.881Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:57.182Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:57.183Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:57.535Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:57.536Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:57.938Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:57.941Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:58.393Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:58.393Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:58.895Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:58.896Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:59.448Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:13:59.448Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:00.050Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:00.050Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:00.702Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:00.702Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:01.412Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:01.414Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:02.167Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:02.167Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:02.970Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:02.970Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:03.822Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:03.822Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:04.723Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:04.724Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:05.676Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:05.677Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:06.678Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:06.678Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:07.730Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:07.730Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:08.832Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:08.833Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:09.983Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:09.984Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:11.185Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:11.186Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:12.438Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:12.438Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:13.740Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:13.740Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:15.091Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:15.091Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:16.493Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:16.494Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:17.947Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:17.948Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:19.452Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:19.452Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:21.006Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:21.007Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:22.608Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:22.608Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:24.260Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:24.261Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:25.962Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:25.962Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:27.715Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:27.716Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:29.518Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:29.519Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:31.371Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:31.373Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:33.277Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:33.277Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:35.228Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:35.228Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:37.231Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:37.234Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:39.235Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:39.236Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:41.238Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:41.238Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:43.241Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:43.242Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:45.244Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:45.245Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:47.247Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:47.247Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:49.249Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:49.250Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:14:52.072Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:15:25.752Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:15:59.608Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:17:06.736Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:19:49.390Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:20:05.482Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:22:52.803Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:23:49.369Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:24:07.640Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:24:25.615Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:25:03.282Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:26:27.008Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:27:36.448Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:28:31.851Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:28:38.364Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:29:24.710Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:29:50.722Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:32:30.724Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:33:03.774Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:33:09.528Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:34:04.792Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:34:25.269Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:35:11.759Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.386Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.391Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.404Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346626,"external":21141899,"heapTotal":111099904,"heapUsed":44456744,"rss":142589952},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.503Z","uptime":0.7911495}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.504Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.509Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.510Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.561Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.561Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.662Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.663Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.814Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:19.815Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.016Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.016Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.267Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.267Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.568Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.569Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.920Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:20.920Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:21.321Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:21.321Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:21.773Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:21.774Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:22.275Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:22.275Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:22.828Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:22.829Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:23.429Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:23.430Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:24.081Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:24.081Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:24.784Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:24.784Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:25.537Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:25.537Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:26.338Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:26.338Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:27.189Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:27.189Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:28.102Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:28.103Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:29.055Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:29.055Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:30.055Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:30.055Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:31.106Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:31.107Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:32.207Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:32.208Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:33.359Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:33.359Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:34.560Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:34.561Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:35.812Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:35.812Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:37.114Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:37.114Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:38.466Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:38.466Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:39.867Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:39.867Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:41.318Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:41.318Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:42.820Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:42.820Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:44.371Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:44.372Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:45.975Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:45.975Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:47.626Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:47.627Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:49.340Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:49.341Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:51.091Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:51.091Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:52.892Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:52.892Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:54.744Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:54.744Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:56.646Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:56.646Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:58.598Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:51:58.598Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:00.599Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:00.599Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:02.601Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:02.601Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:04.604Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:04.605Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:06.606Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:06.607Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:08.608Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:08.608Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:10.610Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:10.610Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:12.612Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:12.613Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:14.615Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:14.616Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:16.616Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:16.617Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:18.619Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:18.619Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:20.621Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:20.622Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:22.624Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:52:22.624Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.096Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.099Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.110Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":19618842,"external":20746029,"heapTotal":77168640,"heapUsed":53581360,"rss":129662976},"message":"Server started successfully on port 5001","nodeVersion":"v18.18.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.217Z","uptime":0.838969041}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.218Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.224Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.225Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.276Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.276Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.381Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.382Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.533Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.534Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.736Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.737Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.989Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:03.990Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:04.293Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:04.293Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:04.645Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:04.646Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:05.047Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:05.047Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:05.499Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:05.499Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:06.000Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:06.000Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:06.551Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:06.552Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:07.154Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:07.154Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:07.805Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:07.805Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:08.507Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:08.508Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:09.258Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:09.258Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:10.060Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:10.060Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:10.911Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:10.912Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:11.812Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:11.813Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:12.765Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:12.765Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:13.767Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:13.768Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:14.819Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:14.819Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:15.920Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:15.920Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:17.072Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:17.072Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:18.276Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:18.277Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:19.527Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:19.527Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:20.830Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:20.831Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:22.184Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:22.185Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:23.587Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:23.587Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:25.038Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:25.038Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:26.540Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:26.540Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:28.093Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:28.094Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:29.695Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:29.695Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:31.348Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:31.349Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:33.050Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:33.050Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:34.803Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:34.803Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:36.605Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:36.605Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:38.457Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:38.457Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:40.359Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:40.359Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:42.311Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:42.311Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:44.314Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:44.314Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:46.317Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:46.317Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:48.319Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:48.319Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:50.321Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:50.321Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:52.323Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:52.323Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:54.326Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:54.326Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:56.328Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:56.328Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:58.330Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:53:58.331Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:00.332Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:00.333Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:02.337Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:02.338Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:04.339Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:04.340Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:06.341Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:06.341Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:08.341Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:08.342Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:10.343Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:10.345Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:12.347Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:12.347Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:14.347Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:14.348Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:16.349Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:16.349Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:18.350Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:18.350Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:20.353Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:20.353Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:22.356Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:22.356Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:24.364Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:24.364Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:26.366Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:26.367Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:28.368Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:28.369Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:30.372Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:30.373Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:32.375Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:32.375Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:34.377Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:34.378Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:36.379Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:36.379Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:38.382Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:38.382Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:40.400Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:40.402Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:42.404Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:42.404Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:44.409Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:44.413Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:46.427Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:46.428Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:48.433Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:48.433Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:50.447Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:50.452Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:52.457Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:52.458Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:54.460Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:54.461Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:56.464Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:56.465Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:58.467Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:54:58.467Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T17:55:00.470Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T17:55:00.472Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:05.834Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:05.838Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:05.851Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346664,"external":21141937,"heapTotal":109264896,"heapUsed":56410304,"rss":137609216},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:05.949Z","uptime":0.71096925}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:05.950Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:05.956Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:05.957Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.007Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.007Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.109Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.109Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.261Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.261Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.466Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.466Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.717Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:06.717Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:07.019Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:07.019Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:07.372Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:07.372Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:07.774Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:07.775Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:08.229Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:08.230Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:08.732Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:08.732Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:09.285Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:09.285Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:09.886Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:09.887Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:10.539Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:10.540Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:11.243Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:11.244Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:11.997Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:11.997Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:12.801Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:12.801Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:13.654Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:13.654Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:14.557Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:14.557Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:15.515Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:15.516Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:16.518Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:16.519Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:17.575Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:17.576Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:18.679Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:18.679Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:19.832Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:19.833Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:21.037Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:21.037Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:22.289Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:22.289Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:23.593Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:23.593Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:24.947Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:24.948Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:26.349Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:26.349Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:27.802Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:27.803Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:29.305Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:29.306Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:30.857Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:30.858Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:32.462Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:32.462Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:34.114Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:34.114Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:35.816Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:35.817Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:37.569Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:37.570Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:39.372Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:39.373Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:41.227Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:41.228Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:43.130Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:43.131Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:45.083Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:45.083Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:47.085Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:47.085Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:49.087Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:49.087Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:51.089Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:51.090Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:53.092Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:53.092Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:55.094Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:55.094Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:57.095Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:57.095Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:59.098Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:15:59.099Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:01.102Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:01.103Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:03.104Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:03.104Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:05.106Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:05.107Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:07.107Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:07.107Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:09.110Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:09.110Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:11.111Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:11.112Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:13.115Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:13.116Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:15.118Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:15.118Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:17.121Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:17.121Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:19.123Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:19.123Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:21.127Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:21.128Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:23.129Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:23.129Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:25.131Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:25.132Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:27.134Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:27.134Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:29.135Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:29.135Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:31.137Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:31.137Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:33.140Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:33.140Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:35.142Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:35.143Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:37.144Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:37.145Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:39.146Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:39.146Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:41.148Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:41.148Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:43.149Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:43.150Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:45.151Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:45.151Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:47.153Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:47.153Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:49.156Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:49.157Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:51.157Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:51.157Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:53.158Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:53.159Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:55.160Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:55.161Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:57.162Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:57.162Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:59.165Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:16:59.165Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:01.167Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:01.167Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:03.169Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:03.170Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:05.171Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:05.171Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:07.172Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:07.172Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:09.174Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:09.174Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:11.175Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:11.175Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:13.175Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:13.175Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:15.176Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:15.176Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:17.178Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:17.178Z"}
{"environment":"development","level":"warn","message":"Redis connection error (continuing without Redis):","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:19.188Z"}
{"environment":"development","level":"warn","message":"Redis connection closed","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:19.188Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:20.412Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:20.420Z"}
{"environment":"development","level":"warn","message":"Redis is not configured. Running without Redis in development mode.","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:20.421Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:20.433Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346664,"external":21141937,"heapTotal":107429888,"heapUsed":57496520,"rss":133496832},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:20.560Z","uptime":0.816747625}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T18:17:20.561Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:20.532Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:20.538Z"}
{"environment":"development","level":"warn","message":"Redis is not configured. Running without Redis in development mode.","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:20.538Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:20.550Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346627,"external":21141900,"heapTotal":110051328,"heapUsed":55009624,"rss":135725056},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:20.659Z","uptime":0.848564}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:20.659Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:35.648Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:35.653Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:35.653Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:35.665Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346626,"external":21141899,"heapTotal":111886336,"heapUsed":43857640,"rss":143622144},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:35.761Z","uptime":0.662212125}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T18:18:35.761Z"}
{"environment":"development","level":"info","message":"SIGINT received. Shutting down gracefully...","service":"smartpoultry-backend","timestamp":"2025-08-04T18:19:30.787Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T18:19:40.963Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T18:19:40.967Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-04T18:19:40.967Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T18:19:40.980Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346627,"external":21141900,"heapTotal":110051328,"heapUsed":55430232,"rss":138575872},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T18:19:41.081Z","uptime":0.716495791}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T18:19:41.082Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T18:22:20.564Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T18:22:20.569Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-04T18:22:20.570Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T18:22:20.583Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346626,"external":21141899,"heapTotal":112934912,"heapUsed":42108216,"rss":143360000},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T18:22:20.697Z","uptime":1.005264875}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T18:22:20.698Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-04T18:23:01.549Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-04T18:23:01.556Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-04T18:23:01.557Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-04T18:23:01.575Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346664,"external":21141937,"heapTotal":106381312,"heapUsed":61344032,"rss":133693440},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-04T18:23:01.695Z","uptime":0.946639125}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-04T18:23:01.696Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:10.841Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:25.977Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:27.219Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:27.834Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:28.200Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:45.144Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:45.769Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.134Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.317Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.483Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.665Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.815Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.000Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.149Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.317Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.533Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.701Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.883Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:48.086Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:48.583Z"}
{"environment":"development","level":"info","message":"SIGINT received. Shutting down gracefully...","service":"smartpoultry-backend","timestamp":"2025-08-04T18:27:05.823Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:04.098Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:04.102Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:04.103Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:04.116Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346626,"external":21141899,"heapTotal":111886336,"heapUsed":44064128,"rss":142884864},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:04.236Z","uptime":0.758626875}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:04.237Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:10:04.753Z"}
{"environment":"development","level":"info","message":"SIGINT received. Shutting down gracefully...","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:09.563Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:11.565Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:11.569Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:11.570Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:11.582Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346627,"external":21141900,"heapTotal":110575616,"heapUsed":55203264,"rss":139575296},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:11.684Z","uptime":0.687181708}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:11.684Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:10:14.191Z"}
{"environment":"development","level":"info","message":"SIGINT received. Shutting down gracefully...","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:33.516Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:37.905Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:37.908Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:37.909Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:37.921Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346664,"external":21141937,"heapTotal":106643456,"heapUsed":61200456,"rss":141508608},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:38.017Z","uptime":0.441916375}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-05T04:10:38.018Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:10:40.556Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-05T04:11:43.967Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-05T04:11:43.971Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-05T04:11:43.972Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-05T04:11:43.985Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346626,"external":21141899,"heapTotal":112934912,"heapUsed":43676848,"rss":146259968},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-05T04:11:44.092Z","uptime":0.817197166}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-05T04:11:44.092Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:11:46.628Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:11:47.399Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:13:40.202Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:13:40.847Z"}
{"environment":"development","level":"info","message":"SIGINT received. Shutting down gracefully...","service":"smartpoultry-backend","timestamp":"2025-08-05T04:13:42.633Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-05T04:13:44.381Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-05T04:13:44.385Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-05T04:13:44.385Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-05T04:13:44.397Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346626,"external":21141899,"heapTotal":112672768,"heapUsed":43831816,"rss":144130048},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-05T04:13:44.505Z","uptime":0.7428805}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-05T04:13:44.506Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:13:45.638Z"}
{"environment":"development","level":"info","message":"SIGINT received. Shutting down gracefully...","service":"smartpoultry-backend","timestamp":"2025-08-05T04:14:46.479Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-05T05:01:11.810Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-05T05:01:11.819Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-05T05:01:11.819Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-05T05:01:11.834Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346664,"external":21141937,"heapTotal":107429888,"heapUsed":57823088,"rss":136183808},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-05T05:01:11.961Z","uptime":0.965842209}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-05T05:01:11.962Z"}
{"environment":"development","level":"info","message":"Metrics middleware enabled successfully","service":"smartpoultry-backend","timestamp":"2025-08-05T05:14:11.575Z"}
{"environment":"development","level":"warn","message":"Sentry DSN not provided, error tracking disabled","service":"smartpoultry-backend","timestamp":"2025-08-05T05:14:11.580Z"}
{"environment":"development","level":"info","message":"Redis not configured. Running without Redis.","service":"smartpoultry-backend","timestamp":"2025-08-05T05:14:11.581Z"}
{"environment":"development","level":"warn","message":"Socket.io is currently disabled due to compatibility issues","service":"smartpoultry-backend","timestamp":"2025-08-05T05:14:11.595Z"}
{"arch":"arm64","environment":"development","level":"info","memoryUsage":{"arrayBuffers":18346626,"external":21141899,"heapTotal":112934912,"heapUsed":43813888,"rss":141017088},"message":"Server started successfully on port 5001","nodeVersion":"v24.5.0","platform":"darwin","service":"smartpoultry-backend","timestamp":"2025-08-05T05:14:11.712Z","uptime":0.781685833}
{"environment":"development","level":"info","message":"MongoDB connected successfully!","service":"smartpoultry-backend","timestamp":"2025-08-05T05:14:11.713Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:16.122Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:17.876Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:17.878Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:19.376Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:20.732Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:24.623Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:29.637Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:34.643Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:39.653Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:44.661Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:49.670Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:52.761Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:52.765Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:53.366Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:54.913Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:59.931Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:04.958Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:09.977Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:14.991Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:20.012Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:25.030Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:30.054Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:35.086Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:40.096Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:40.373Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:40.384Z"}
